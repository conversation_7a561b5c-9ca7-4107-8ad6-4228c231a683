'use client';

import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { useAuth } from '@clerk/nextjs';
import { Database } from '@/lib/database.types';

interface SupabaseContextType {
  supabase: SupabaseClient<Database> | null;
  isInitialized: boolean;
}

const SupabaseContext = createContext<SupabaseContextType | undefined>(
  undefined
);

// --- Centralized Supabase Client Instance ---
// This ensures that there is only one instance of the Supabase client throughout the app.
let supabaseInstance: SupabaseClient<Database> | null = null;

export const SupabaseProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { getToken, isSignedIn } = useAuth();
  const [isInitialized, setIsInitialized] = useState(false);

  const supabase = useMemo(() => {
    if (supabaseInstance) {
      return supabaseInstance;
    }

    if (
      !process.env.NEXT_PUBLIC_SUPABASE_URL ||
      !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    ) {
      throw new Error(
        'Supabase URL or Anon Key is not defined in environment variables.'
      );
    }

    supabaseInstance = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        global: {
          fetch: async (url, options = {}) => {
            const token = await getToken({ template: 'supabase' });
            const headers = new Headers(options.headers);
            if (token) {
              headers.set('Authorization', `Bearer ${token}`);
            }
            return fetch(url, { ...options, headers });
          },
        },
        realtime: {
          // 2025 Enhanced real-time configuration for maximum connection stability
          params: {
            eventsPerSecond: 15, // Balanced for performance and stability
            log_level: 'info', // Enable detailed logging for debugging
          },
          heartbeatIntervalMs: 15000, // 15 seconds heartbeat for aggressive persistence
          reconnectAfterMs: (tries: number) => {
            // Intelligent reconnection strategy based on attempt count
            if (tries <= 2) {
              return 100; // Immediate retry for first 2 attempts
            } else if (tries <= 5) {
              return Math.min(tries * 500, 2500); // Fast backoff for attempts 3-5
            } else {
              return Math.min(tries * 1000, 15000); // Standard backoff, max 15s
            }
          },
          timeout: 10000, // 10 second timeout for connection establishment
        },
      }
    );

    return supabaseInstance;
  }, [getToken]);

  useEffect(() => {
    if (supabase && isSignedIn) {
      setIsInitialized(true);
    } else {
      setIsInitialized(false);
    }
  }, [supabase, isSignedIn]);

  return (
    <SupabaseContext.Provider value={{ supabase, isInitialized }}>
      {children}
    </SupabaseContext.Provider>
  );
};

export const useSupabase = () => {
  const context = useContext(SupabaseContext);
  if (context === undefined) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  return context;
};

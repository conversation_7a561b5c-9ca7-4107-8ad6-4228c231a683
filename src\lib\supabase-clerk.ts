'use client';

import { createClient } from '@supabase/supabase-js';
import { useSession, useUser } from '@clerk/nextjs';
import { useCallback, useMemo } from 'react';
import type { Database } from '@/types/supabase';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// CRITICAL FIX: Global singleton Supabase client to prevent multiple WebSocket connections
let globalSupabaseClient: ReturnType<typeof createClient<Database>> | null =
  null;

/**
 * Hook to create a Supabase client with Clerk authentication
 * Uses Clerk's native Supabase integration for secure token management
 * SINGLETON PATTERN: Ensures only one client instance exists globally
 */
export function useSupabaseClient() {
  const { session } = useSession();
  const { user } = useUser();

  const supabaseClient = useMemo(() => {
    // CRITICAL: Reuse existing client if available to prevent multiple WebSocket connections
    if (globalSupabaseClient) {
      console.log('♻️ Reusing existing global Supabase client');
      return globalSupabaseClient;
    }

    console.log('🔗 Creating new global Supabase client');
    globalSupabaseClient = createClient<Database>(
      supabaseUrl,
      supabaseAnonKey,
      {
        async accessToken() {
          try {
            // CRITICAL FIX: Enhanced authentication guard with better error handling
            if (!session || !session.status || session.status !== 'active') {
              // Don't log warnings for expected states during login/logout
              return null;
            }

            // Get token with template for Supabase
            const token = await session.getToken({ template: 'supabase' });
            return token ?? null;
          } catch (error) {
            // Only log actual errors, not expected authentication states
            if (session?.status === 'active') {
              console.warn(
                'Session token access failed for active session:',
                error
              );
            }
            return null;
          }
        },
        realtime: {
          // 2025 Enhanced real-time configuration for maximum connection stability
          params: {
            eventsPerSecond: 15, // Balanced for performance and stability
            log_level: 'info', // Enable detailed logging for debugging
          },
          heartbeatIntervalMs: 15000, // 15 seconds heartbeat for aggressive persistence
          reconnectAfterMs: (tries: number) => {
            // Intelligent reconnection strategy based on attempt count
            if (tries <= 2) {
              return 100; // Immediate retry for first 2 attempts
            } else if (tries <= 5) {
              return Math.min(tries * 500, 2500); // Fast backoff for attempts 3-5
            } else {
              return Math.min(tries * 1000, 15000); // Standard backoff, max 15s
            }
          },
          // 2025 Enhancement: Additional WebSocket-level optimizations
          timeout: 10000, // 10 second timeout for connection establishment
        },
      }
    );

    return globalSupabaseClient;
  }, [session]); // Include session dependency for proper token access

  return {
    supabase: supabaseClient,
    user,
    isAuthenticated: !!user,
  };
}

/**
 * Hook for fetching tickets with proper authentication and tenant isolation
 */
export function useTickets(tenantId: string | null) {
  const { supabase, isAuthenticated } = useSupabaseClient();

  const fetchTickets = useCallback(async () => {
    if (!isAuthenticated || !tenantId) {
      return [];
    }

    const { data, error } = await supabase
      .from('tickets')
      .select('*')
      .eq('tenant_id', tenantId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch tickets: ${error.message}`);
    }

    return data || [];
  }, [supabase, tenantId, isAuthenticated]);

  return { fetchTickets, isAuthenticated };
}

/**
 * Hook for updating tickets with proper authentication and tenant isolation
 */
export function useUpdateTicket() {
  const { supabase, user, isAuthenticated } = useSupabaseClient();

  const updateTicket = useCallback(
    async (
      ticketId: string,
      updates: {
        title?: string;
        description?: string;
        status?: string;
        priority?: string;
        department?: string;
      }
    ) => {
      if (!isAuthenticated || !user) {
        throw new Error('User must be authenticated to update tickets');
      }

      const { data, error } = await supabase
        .from('tickets')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', ticketId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update ticket: ${error.message}`);
      }

      return data;
    },
    [supabase, user, isAuthenticated]
  );

  return { updateTicket, isAuthenticated };
}

/**
 * Server-side Supabase client creation utility
 * For use in API routes and server components
 */
export function createServerSupabaseClient(authToken: string | null) {
  return createClient<Database>(supabaseUrl, supabaseAnonKey, {
    async accessToken() {
      return authToken;
    },
  });
}
